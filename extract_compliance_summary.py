#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取测评报告中各测评对象的符合数量 - 改进版
"""

import docx
import re
from collections import defaultdict

def extract_compliance_summary(docx_file):
    """
    从Word文档中提取各测评对象的符合数量汇总
    """
    doc = docx.Document(docx_file)
    
    # 存储结果
    compliance_summary = {}
    
    print("开始分析表格数据...")
    print("=" * 60)
    
    # 分析每个表格
    for table_idx, table in enumerate(doc.tables):
        print(f"\n分析表格 {table_idx + 1}:")
        
        # 获取表格的所有行数据
        table_data = []
        for row in table.rows:
            row_data = []
            for cell in row.cells:
                cell_text = cell.text.strip()
                row_data.append(cell_text)
            table_data.append(row_data)
        
        if len(table_data) < 2:
            continue
            
        # 分析表格结构，寻找符合情况统计
        category_name = None
        
        # 从表格数据中识别类别
        for row in table_data:
            row_text = " ".join(row)
            if "安全物理环境" in row_text:
                category_name = "安全物理环境"
                break
            elif "安全通信网络" in row_text:
                category_name = "安全通信网络"
                break
            elif "安全区域边界" in row_text:
                category_name = "安全区域边界"
                break
            elif "安全计算环境" in row_text:
                category_name = "安全计算环境"
                break
            elif "安全管理中心" in row_text:
                category_name = "安全管理中心"
                break
            elif "安全管理制度" in row_text:
                category_name = "安全管理制度"
                break
            elif "安全管理机构" in row_text:
                category_name = "安全管理机构"
                break
            elif "安全管理人员" in row_text:
                category_name = "安全管理人员"
                break
            elif "安全建设管理" in row_text:
                category_name = "安全建设管理"
                break
            elif "安全运维管理" in row_text:
                category_name = "安全运维管理"
                break
        
        if not category_name:
            continue
            
        print(f"  识别类别: {category_name}")
        
        # 统计符合情况
        compliance_count = 0
        partial_compliance_count = 0
        non_compliance_count = 0
        not_applicable_count = 0
        
        # 查找包含"符合"的行
        for row in table_data:
            if len(row) > 2 and "符合" in row[2]:
                # 统计该行的数字
                numbers = []
                for cell in row[3:]:  # 从第4列开始统计数字
                    if cell.isdigit():
                        numbers.append(int(cell))
                
                if "符合" == row[2].strip():
                    compliance_count += sum(numbers)
                elif "部分符合" in row[2]:
                    partial_compliance_count += sum(numbers)
                elif "不符合" in row[2]:
                    non_compliance_count += sum(numbers)
                elif "不适用" in row[2]:
                    not_applicable_count += sum(numbers)
        
        total_items = compliance_count + partial_compliance_count + non_compliance_count
        
        compliance_summary[category_name] = {
            '符合项数': compliance_count,
            '部分符合项数': partial_compliance_count,
            '不符合项数': non_compliance_count,
            '不适用项数': not_applicable_count,
            '总测评项数': total_items,
            '符合率': (compliance_count / total_items * 100) if total_items > 0 else 0
        }
        
        print(f"  符合: {compliance_count} 项")
        print(f"  部分符合: {partial_compliance_count} 项")
        print(f"  不符合: {non_compliance_count} 项")
        print(f"  不适用: {not_applicable_count} 项")
        print(f"  总测评项数: {total_items} 项")
        if total_items > 0:
            print(f"  符合率: {compliance_count / total_items * 100:.1f}%")
    
    return compliance_summary

def print_final_summary(compliance_summary):
    """
    打印最终汇总报告
    """
    print("\n" + "=" * 80)
    print("测评对象符合数量最终汇总报告")
    print("=" * 80)
    
    total_compliance = 0
    total_partial_compliance = 0
    total_non_compliance = 0
    total_not_applicable = 0
    total_items = 0
    
    # 按类别排序
    categories = [
        "安全物理环境", "安全通信网络", "安全区域边界", "安全计算环境",
        "安全管理中心", "安全管理制度", "安全管理机构", "安全管理人员",
        "安全建设管理", "安全运维管理"
    ]
    
    for category in categories:
        if category in compliance_summary:
            data = compliance_summary[category]
            print(f"\n【{category}】")
            print("-" * 50)
            print(f"  符合项数: {data['符合项数']}")
            print(f"  部分符合项数: {data['部分符合项数']}")
            print(f"  不符合项数: {data['不符合项数']}")
            print(f"  不适用项数: {data['不适用项数']}")
            print(f"  总测评项数: {data['总测评项数']}")
            print(f"  符合率: {data['符合率']:.1f}%")
            
            total_compliance += data['符合项数']
            total_partial_compliance += data['部分符合项数']
            total_non_compliance += data['不符合项数']
            total_not_applicable += data['不适用项数']
            total_items += data['总测评项数']
    
    print("\n" + "=" * 80)
    print("总体统计")
    print("=" * 80)
    print(f"总符合项数: {total_compliance}")
    print(f"总部分符合项数: {total_partial_compliance}")
    print(f"总不符合项数: {total_non_compliance}")
    print(f"总不适用项数: {total_not_applicable}")
    print(f"总测评项数: {total_items}")
    if total_items > 0:
        print(f"总体符合率: {total_compliance / total_items * 100:.1f}%")
        print(f"总体符合+部分符合率: {(total_compliance + total_partial_compliance) / total_items * 100:.1f}%")
    
    return {
        '总符合项数': total_compliance,
        '总部分符合项数': total_partial_compliance,
        '总不符合项数': total_non_compliance,
        '总不适用项数': total_not_applicable,
        '总测评项数': total_items,
        '总体符合率': total_compliance / total_items * 100 if total_items > 0 else 0
    }

def save_detailed_report(compliance_summary, filename='compliance_final_report.txt'):
    """
    保存详细报告到文件
    """
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("测评对象符合数量详细报告\n")
        f.write("=" * 80 + "\n\n")
        
        categories = [
            "安全物理环境", "安全通信网络", "安全区域边界", "安全计算环境",
            "安全管理中心", "安全管理制度", "安全管理机构", "安全管理人员",
            "安全建设管理", "安全运维管理"
        ]
        
        for category in categories:
            if category in compliance_summary:
                data = compliance_summary[category]
                f.write(f"【{category}】\n")
                f.write("-" * 50 + "\n")
                f.write(f"符合项数: {data['符合项数']}\n")
                f.write(f"部分符合项数: {data['部分符合项数']}\n")
                f.write(f"不符合项数: {data['不符合项数']}\n")
                f.write(f"不适用项数: {data['不适用项数']}\n")
                f.write(f"总测评项数: {data['总测评项数']}\n")
                f.write(f"符合率: {data['符合率']:.1f}%\n\n")

if __name__ == "__main__":
    try:
        # 提取数据
        compliance_summary = extract_compliance_summary('测评报告.docx')
        
        # 打印汇总
        final_stats = print_final_summary(compliance_summary)
        
        # 保存详细报告
        save_detailed_report(compliance_summary)
        print(f"\n详细报告已保存到 compliance_final_report.txt")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
