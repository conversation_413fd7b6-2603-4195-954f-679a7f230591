#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取测评报告中各测评对象的符合数量
"""

import docx
import re
from collections import defaultdict

def extract_compliance_data(docx_file):
    """
    从Word文档中提取各测评对象的符合数量
    """
    doc = docx.Document(docx_file)

    # 存储结果
    compliance_data = {}
    current_category = None
    current_object = None

    # 用于匹配符合数量的正则表达式
    compliance_pattern = r'符合.*?(\d+).*?项'
    total_pattern = r'共.*?(\d+).*?项'

    print("开始提取测评数据...")
    print("=" * 50)

    # 首先检查段落
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if not text:
            continue

        print(f"段落{i}: {text}")

        # 识别主要类别
        if "安全物理环境" in text and "表" not in text:
            current_category = "安全物理环境"
            compliance_data[current_category] = {}
        elif "安全通信网络" in text and "表" not in text:
            current_category = "安全通信网络"
            compliance_data[current_category] = {}
        elif "安全区域边界" in text and "表" not in text:
            current_category = "安全区域边界"
            compliance_data[current_category] = {}
        elif "安全计算环境" in text and "表" not in text:
            current_category = "安全计算环境"
            compliance_data[current_category] = {}
        elif "安全管理中心" in text and "表" not in text:
            current_category = "安全管理中心"
            compliance_data[current_category] = {}
        elif "安全管理制度" in text and "表" not in text:
            current_category = "安全管理制度"
            compliance_data[current_category] = {}
        elif "安全管理机构" in text and "表" not in text:
            current_category = "安全管理机构"
            compliance_data[current_category] = {}
        elif "安全管理人员" in text and "表" not in text:
            current_category = "安全管理人员"
            compliance_data[current_category] = {}
        elif "安全建设管理" in text and "表" not in text:
            current_category = "安全建设管理"
            compliance_data[current_category] = {}
        elif "安全运维管理" in text and "表" not in text:
            current_category = "安全运维管理"
            compliance_data[current_category] = {}

        # 识别具体测评对象
        elif "网络设备" in text and "表" not in text:
            current_object = "网络设备"
        elif "安全设备" in text and "表" not in text:
            current_object = "安全设备"
        elif "服务器和终端" in text and "表" not in text:
            current_object = "服务器和终端"
        elif "系统管理软件/平台" in text and "表" not in text:
            current_object = "系统管理软件/平台"
        elif "业务应用系统/平台" in text and "表" not in text:
            current_object = "业务应用系统/平台"
        elif "数据资源" in text and "表" not in text:
            current_object = "数据资源"

        # 提取符合数量信息
        if "符合" in text and "项" in text:
            compliance_match = re.search(compliance_pattern, text)
            total_match = re.search(total_pattern, text)

            if compliance_match:
                compliance_count = int(compliance_match.group(1))
                total_count = None
                if total_match:
                    total_count = int(total_match.group(1))

                if current_category and current_object:
                    if current_category not in compliance_data:
                        compliance_data[current_category] = {}
                    compliance_data[current_category][current_object] = {
                        '符合项数': compliance_count,
                        '总项数': total_count,
                        '原文': text
                    }
                elif current_category:
                    if current_category not in compliance_data:
                        compliance_data[current_category] = {}
                    compliance_data[current_category]['总计'] = {
                        '符合项数': compliance_count,
                        '总项数': total_count,
                        '原文': text
                    }

    print("\n检查表格内容...")
    print("=" * 50)

    # 检查表格内容
    for table_idx, table in enumerate(doc.tables):
        print(f"\n表格 {table_idx + 1}:")
        for row_idx, row in enumerate(table.rows):
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip()
                if cell_text:
                    row_text.append(cell_text)

            if row_text:
                full_row_text = " | ".join(row_text)
                print(f"  行{row_idx}: {full_row_text}")

                # 在表格中查找符合数量信息
                if "符合" in full_row_text and ("项" in full_row_text or any(char.isdigit() for char in full_row_text)):
                    # 尝试提取数字
                    numbers = re.findall(r'\d+', full_row_text)
                    if numbers and len(numbers) >= 2:
                        try:
                            # 假设最后两个数字是符合数和总数
                            compliance_count = int(numbers[-2])
                            total_count = int(numbers[-1])

                            # 尝试识别这是哪个类别的数据
                            category_key = None
                            if "物理环境" in full_row_text:
                                category_key = "安全物理环境"
                            elif "通信网络" in full_row_text:
                                category_key = "安全通信网络"
                            elif "区域边界" in full_row_text:
                                category_key = "安全区域边界"
                            elif "计算环境" in full_row_text:
                                category_key = "安全计算环境"
                            elif "管理中心" in full_row_text:
                                category_key = "安全管理中心"
                            elif "管理制度" in full_row_text:
                                category_key = "安全管理制度"
                            elif "管理机构" in full_row_text:
                                category_key = "安全管理机构"
                            elif "管理人员" in full_row_text:
                                category_key = "安全管理人员"
                            elif "建设管理" in full_row_text:
                                category_key = "安全建设管理"
                            elif "运维管理" in full_row_text:
                                category_key = "安全运维管理"

                            if category_key:
                                if category_key not in compliance_data:
                                    compliance_data[category_key] = {}
                                compliance_data[category_key]['汇总'] = {
                                    '符合项数': compliance_count,
                                    '总项数': total_count,
                                    '原文': full_row_text
                                }
                                print(f"    -> 提取到: {category_key} 符合{compliance_count}项/共{total_count}项")
                        except ValueError:
                            pass

    return compliance_data

def print_compliance_summary(compliance_data):
    """
    打印符合数量汇总
    """
    print("\n" + "=" * 60)
    print("测评对象符合数量汇总")
    print("=" * 60)
    
    total_compliance = 0
    total_items = 0
    
    for category, objects in compliance_data.items():
        print(f"\n【{category}】")
        print("-" * 40)
        
        category_compliance = 0
        category_total = 0
        
        for obj_name, data in objects.items():
            compliance_count = data['符合项数']
            total_count = data['总项数'] if data['总项数'] else '未知'
            
            print(f"  {obj_name}: 符合 {compliance_count} 项")
            if data['总项数']:
                print(f"    (共 {total_count} 项，符合率: {compliance_count/data['总项数']*100:.1f}%)")
                category_total += data['总项数']
            else:
                print(f"    (总项数未明确)")
            
            category_compliance += compliance_count
            total_compliance += compliance_count
            if data['总项数']:
                total_items += data['总项数']
        
        if category_total > 0:
            print(f"  类别小计: 符合 {category_compliance} 项 / 共 {category_total} 项 (符合率: {category_compliance/category_total*100:.1f}%)")
    
    print("\n" + "=" * 60)
    print("总体汇总")
    print("=" * 60)
    print(f"总符合项数: {total_compliance}")
    if total_items > 0:
        print(f"总测评项数: {total_items}")
        print(f"总体符合率: {total_compliance/total_items*100:.1f}%")
    
    return {
        '总符合项数': total_compliance,
        '总测评项数': total_items,
        '总体符合率': total_compliance/total_items*100 if total_items > 0 else 0
    }

if __name__ == "__main__":
    try:
        # 提取数据
        compliance_data = extract_compliance_data('测评报告.docx')
        
        # 打印汇总
        summary = print_compliance_summary(compliance_data)
        
        # 保存详细数据到文件
        with open('compliance_details.txt', 'w', encoding='utf-8') as f:
            f.write("测评对象符合数量详细信息\n")
            f.write("=" * 60 + "\n\n")
            
            for category, objects in compliance_data.items():
                f.write(f"【{category}】\n")
                f.write("-" * 40 + "\n")
                
                for obj_name, data in objects.items():
                    f.write(f"测评对象: {obj_name}\n")
                    f.write(f"符合项数: {data['符合项数']}\n")
                    f.write(f"总项数: {data['总项数'] if data['总项数'] else '未明确'}\n")
                    f.write(f"原文: {data['原文']}\n")
                    f.write("\n")
                f.write("\n")
        
        print(f"\n详细信息已保存到 compliance_details.txt")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
