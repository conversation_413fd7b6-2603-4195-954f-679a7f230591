#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的测评报告符合数量分析
"""

import docx
import re
from collections import defaultdict

def analyze_all_tables(docx_file):
    """
    分析所有表格，提取完整的符合数量信息
    """
    doc = docx.Document(docx_file)
    
    # 存储结果
    results = {}
    
    print("开始完整分析所有表格...")
    print("=" * 80)
    
    # 分析每个表格
    for table_idx, table in enumerate(doc.tables):
        print(f"\n表格 {table_idx + 1}:")
        
        # 获取表格的所有行数据
        table_data = []
        for row in table.rows:
            row_data = []
            for cell in row.cells:
                cell_text = cell.text.strip()
                row_data.append(cell_text)
            table_data.append(row_data)
        
        if len(table_data) < 2:
            continue
        
        # 打印表格前几行以便分析
        for i, row in enumerate(table_data[:3]):
            print(f"  行{i}: {' | '.join(row[:8])}...")  # 只显示前8列
        
        # 识别表格类型和统计数据
        category_name = identify_category(table_data)
        if category_name:
            print(f"  -> 识别为: {category_name}")
            stats = calculate_statistics(table_data, category_name)
            if stats:
                results[category_name] = stats
                print(f"  -> 统计结果: 符合{stats['符合项数']}项, 部分符合{stats['部分符合项数']}项, 不符合{stats['不符合项数']}项")
    
    return results

def identify_category(table_data):
    """
    识别表格所属的测评类别
    """
    # 检查表格中的文本内容
    all_text = " ".join([" ".join(row) for row in table_data])
    
    if "物理环境" in all_text or "主机房" in all_text:
        return "安全物理环境"
    elif "通信网络" in all_text and "网络架构" in all_text:
        return "安全通信网络"
    elif "区域边界" in all_text and "边界防护" in all_text:
        return "安全区域边界"
    elif "计算环境" in all_text and ("网络设备" in all_text or "服务器" in all_text or "身份鉴别" in all_text):
        # 进一步区分网络设备、安全设备、服务器等
        if "交换机" in all_text or "负载均衡" in all_text:
            return "安全计算环境-网络设备"
        elif "防火墙" in all_text:
            return "安全计算环境-安全设备"
        elif "服务器" in all_text:
            return "安全计算环境-服务器和终端"
        elif "数据库" in all_text or "MySQL" in all_text or "Redis" in all_text:
            return "安全计算环境-系统管理软件/平台"
        elif "子系统" in all_text:
            return "安全计算环境-业务应用系统/平台"
        else:
            return "安全计算环境"
    elif "数据资源" in all_text or ("业务数据" in all_text and "个人数据" in all_text):
        return "数据资源"
    elif "管理中心" in all_text and "系统管理" in all_text:
        return "安全管理中心"
    elif "管理制度" in all_text and "安全策略" in all_text:
        return "安全管理制度"
    elif "管理机构" in all_text and "岗位设置" in all_text:
        return "安全管理机构"
    elif "管理人员" in all_text and "人员录用" in all_text:
        return "安全管理人员"
    elif "建设管理" in all_text and "定级和备案" in all_text:
        return "安全建设管理"
    elif "运维管理" in all_text and "环境管理" in all_text:
        return "安全运维管理"
    
    return None

def calculate_statistics(table_data, category_name):
    """
    计算表格中的统计数据
    """
    compliance_count = 0
    partial_compliance_count = 0
    non_compliance_count = 0
    not_applicable_count = 0
    
    # 查找包含符合情况的行
    for row in table_data:
        if len(row) > 2:
            compliance_status = row[2].strip() if len(row) > 2 else ""
            
            # 统计该行的数字
            numbers = []
            for cell in row[3:]:  # 从第4列开始统计数字
                if cell.isdigit():
                    numbers.append(int(cell))
            
            if compliance_status == "符合":
                compliance_count += sum(numbers)
            elif "部分符合" in compliance_status:
                partial_compliance_count += sum(numbers)
            elif "不符合" in compliance_status:
                non_compliance_count += sum(numbers)
            elif "不适用" in compliance_status:
                not_applicable_count += sum(numbers)
    
    total_items = compliance_count + partial_compliance_count + non_compliance_count
    
    if total_items > 0:
        return {
            '符合项数': compliance_count,
            '部分符合项数': partial_compliance_count,
            '不符合项数': non_compliance_count,
            '不适用项数': not_applicable_count,
            '总测评项数': total_items,
            '符合率': (compliance_count / total_items * 100) if total_items > 0 else 0
        }
    
    return None

def generate_final_report(results):
    """
    生成最终报告
    """
    print("\n" + "=" * 100)
    print("测评对象符合数量完整汇总报告")
    print("=" * 100)
    
    # 按类别分组
    categories = {
        "技术要求": [
            "安全物理环境",
            "安全通信网络", 
            "安全区域边界",
            "安全计算环境-网络设备",
            "安全计算环境-安全设备", 
            "安全计算环境-服务器和终端",
            "安全计算环境-系统管理软件/平台",
            "安全计算环境-业务应用系统/平台",
            "数据资源"
        ],
        "管理要求": [
            "安全管理中心",
            "安全管理制度",
            "安全管理机构", 
            "安全管理人员",
            "安全建设管理",
            "安全运维管理"
        ]
    }
    
    total_stats = {
        '符合项数': 0,
        '部分符合项数': 0,
        '不符合项数': 0,
        '不适用项数': 0,
        '总测评项数': 0
    }
    
    for group_name, category_list in categories.items():
        print(f"\n【{group_name}】")
        print("=" * 60)
        
        group_stats = {
            '符合项数': 0,
            '部分符合项数': 0,
            '不符合项数': 0,
            '不适用项数': 0,
            '总测评项数': 0
        }
        
        for category in category_list:
            if category in results:
                data = results[category]
                print(f"\n  {category}:")
                print(f"    符合项数: {data['符合项数']}")
                print(f"    部分符合项数: {data['部分符合项数']}")
                print(f"    不符合项数: {data['不符合项数']}")
                print(f"    总测评项数: {data['总测评项数']}")
                print(f"    符合率: {data['符合率']:.1f}%")
                
                # 累加到组统计
                for key in group_stats:
                    group_stats[key] += data[key]
                
                # 累加到总统计
                for key in total_stats:
                    total_stats[key] += data[key]
        
        # 打印组统计
        if group_stats['总测评项数'] > 0:
            print(f"\n  {group_name}小计:")
            print(f"    符合项数: {group_stats['符合项数']}")
            print(f"    部分符合项数: {group_stats['部分符合项数']}")
            print(f"    不符合项数: {group_stats['不符合项数']}")
            print(f"    总测评项数: {group_stats['总测评项数']}")
            print(f"    符合率: {group_stats['符合项数'] / group_stats['总测评项数'] * 100:.1f}%")
    
    # 打印总统计
    print("\n" + "=" * 100)
    print("总体统计")
    print("=" * 100)
    print(f"总符合项数: {total_stats['符合项数']}")
    print(f"总部分符合项数: {total_stats['部分符合项数']}")
    print(f"总不符合项数: {total_stats['不符合项数']}")
    print(f"总不适用项数: {total_stats['不适用项数']}")
    print(f"总测评项数: {total_stats['总测评项数']}")
    
    if total_stats['总测评项数'] > 0:
        print(f"总体符合率: {total_stats['符合项数'] / total_stats['总测评项数'] * 100:.1f}%")
        print(f"总体符合+部分符合率: {(total_stats['符合项数'] + total_stats['部分符合项数']) / total_stats['总测评项数'] * 100:.1f}%")
    
    return total_stats

if __name__ == "__main__":
    try:
        # 分析所有表格
        results = analyze_all_tables('测评报告.docx')
        
        # 生成最终报告
        total_stats = generate_final_report(results)
        
        # 保存到文件
        with open('complete_compliance_report.txt', 'w', encoding='utf-8') as f:
            f.write("测评对象符合数量完整报告\n")
            f.write("=" * 80 + "\n\n")
            
            for category, data in results.items():
                f.write(f"【{category}】\n")
                f.write(f"符合项数: {data['符合项数']}\n")
                f.write(f"部分符合项数: {data['部分符合项数']}\n")
                f.write(f"不符合项数: {data['不符合项数']}\n")
                f.write(f"不适用项数: {data['不适用项数']}\n")
                f.write(f"总测评项数: {data['总测评项数']}\n")
                f.write(f"符合率: {data['符合率']:.1f}%\n\n")
            
            f.write("总体统计:\n")
            f.write(f"总符合项数: {total_stats['符合项数']}\n")
            f.write(f"总部分符合项数: {total_stats['部分符合项数']}\n")
            f.write(f"总不符合项数: {total_stats['不符合项数']}\n")
            f.write(f"总测评项数: {total_stats['总测评项数']}\n")
            if total_stats['总测评项数'] > 0:
                f.write(f"总体符合率: {total_stats['符合项数'] / total_stats['总测评项数'] * 100:.1f}%\n")
        
        print(f"\n完整报告已保存到 complete_compliance_report.txt")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
