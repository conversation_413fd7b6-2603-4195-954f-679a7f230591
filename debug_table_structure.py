#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试表格结构，找出为什么某些表格没有统计到数据
"""

import docx

def debug_table_structure(docx_file):
    """
    调试表格结构
    """
    doc = docx.Document(docx_file)
    
    # 手动定义表格和类别的映射关系
    table_category_mapping = {
        0: "安全物理环境",
        1: "安全通信网络",
        2: "安全区域边界",
        3: "安全计算环境-网络设备",
        4: "安全计算环境-安全设备",
        5: "安全计算环境-服务器和终端",
        6: "安全计算环境-系统管理软件/平台",
        7: "安全计算环境-业务应用系统/平台",
        8: "数据资源",
        9: "安全管理中心",
        10: "安全管理制度",
        11: "安全管理机构",
        12: "安全管理人员",
        13: "安全建设管理",
        14: "安全运维管理"
    }
    
    tables = doc.tables
    
    # 重点检查有问题的表格
    problem_tables = [0, 3, 13]  # 安全物理环境、网络设备、安全建设管理
    
    for table_idx in problem_tables:
        if table_idx < len(tables):
            category = table_category_mapping[table_idx]
            table = tables[table_idx]
            
            print(f"\n{'='*80}")
            print(f"表格 {table_idx}: {category}")
            print(f"{'='*80}")
            
            # 获取表格数据
            table_data = []
            for row in table.rows:
                row_data = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    row_data.append(cell_text)
                table_data.append(row_data)
            
            # 打印所有行
            for i, row in enumerate(table_data):
                print(f"行{i}: {row}")
            
            print(f"\n分析统计逻辑:")
            
            # 分析统计逻辑
            compliance_count = 0
            partial_compliance_count = 0
            non_compliance_count = 0
            not_applicable_count = 0
            
            for i, row in enumerate(table_data):
                if len(row) > 2:
                    compliance_status = row[2].strip() if len(row) > 2 else ""
                    print(f"  行{i}: 符合情况='{compliance_status}'")
                    
                    # 统计该行的数字
                    numbers = []
                    for j, cell in enumerate(row[3:], 3):  # 从第4列开始统计数字
                        if cell.isdigit():
                            numbers.append(int(cell))
                            print(f"    列{j}: '{cell}' -> {int(cell)}")
                    
                    print(f"    数字总和: {sum(numbers)}")
                    
                    if compliance_status == "符合":
                        compliance_count += sum(numbers)
                        print(f"    -> 累加到符合项: {sum(numbers)}")
                    elif "部分符合" in compliance_status:
                        partial_compliance_count += sum(numbers)
                        print(f"    -> 累加到部分符合项: {sum(numbers)}")
                    elif "不符合" in compliance_status:
                        non_compliance_count += sum(numbers)
                        print(f"    -> 累加到不符合项: {sum(numbers)}")
                    elif "不适用" in compliance_status:
                        not_applicable_count += sum(numbers)
                        print(f"    -> 累加到不适用项: {sum(numbers)}")
            
            total_items = compliance_count + partial_compliance_count + non_compliance_count
            
            print(f"\n最终统计:")
            print(f"  符合项数: {compliance_count}")
            print(f"  部分符合项数: {partial_compliance_count}")
            print(f"  不符合项数: {non_compliance_count}")
            print(f"  不适用项数: {not_applicable_count}")
            print(f"  总测评项数: {total_items}")
            
            if total_items > 0:
                print(f"  符合率: {compliance_count / total_items * 100:.1f}%")
                print(f"  -> 有效数据，应该被统计")
            else:
                print(f"  -> 无效数据，不会被统计")

def analyze_all_tables(docx_file):
    """
    分析所有表格的基本信息
    """
    doc = docx.Document(docx_file)
    
    # 手动定义表格和类别的映射关系
    table_category_mapping = {
        0: "安全物理环境",
        1: "安全通信网络",
        2: "安全区域边界",
        3: "安全计算环境-网络设备",
        4: "安全计算环境-安全设备",
        5: "安全计算环境-服务器和终端",
        6: "安全计算环境-系统管理软件/平台",
        7: "安全计算环境-业务应用系统/平台",
        8: "数据资源",
        9: "安全管理中心",
        10: "安全管理制度",
        11: "安全管理机构",
        12: "安全管理人员",
        13: "安全建设管理",
        14: "安全运维管理"
    }
    
    tables = doc.tables
    
    print(f"总共有 {len(tables)} 个表格")
    print(f"{'='*100}")
    
    for table_idx, table in enumerate(tables):
        if table_idx in table_category_mapping:
            category = table_category_mapping[table_idx]
            
            # 获取表格数据
            table_data = []
            for row in table.rows:
                row_data = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    row_data.append(cell_text)
                table_data.append(row_data)
            
            print(f"表格 {table_idx}: {category}")
            print(f"  行数: {len(table_data)}")
            print(f"  列数: {len(table_data[0]) if table_data else 0}")
            
            # 检查是否有符合情况列
            has_compliance_column = False
            if len(table_data) > 1 and len(table_data[1]) > 2:
                if "符合情况" in table_data[0][2] or "符合情况" in table_data[1][2]:
                    has_compliance_column = True
            
            print(f"  有符合情况列: {has_compliance_column}")
            
            # 检查是否有数字数据
            has_numeric_data = False
            for row in table_data[2:]:  # 从第3行开始检查
                for cell in row[3:]:  # 从第4列开始检查
                    if cell.isdigit():
                        has_numeric_data = True
                        break
                if has_numeric_data:
                    break
            
            print(f"  有数字数据: {has_numeric_data}")
            print()

if __name__ == "__main__":
    print("分析所有表格的基本信息:")
    analyze_all_tables('测评报告.docx')
    
    print("\n详细调试有问题的表格:")
    debug_table_structure('测评报告.docx')
