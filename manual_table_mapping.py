#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动映射表格和测评对象类别
"""

import docx
import re
from collections import defaultdict

def extract_compliance_manual_mapping(docx_file):
    """
    手动映射表格和测评对象类别
    """
    doc = docx.Document(docx_file)
    
    # 手动定义表格和类别的映射关系
    table_category_mapping = {
        0: "安全物理环境",
        1: "安全通信网络",
        2: "安全区域边界",
        3: "安全计算环境-网络设备",
        4: "安全计算环境-安全设备",
        5: "安全计算环境-服务器和终端",
        6: "安全计算环境-系统管理软件/平台",
        7: "安全计算环境-业务应用系统/平台",
        8: "数据资源",
        9: "安全管理中心",
        10: "安全管理制度",
        11: "安全管理机构",
        12: "安全管理人员",
        13: "安全建设管理",
        14: "安全运维管理"
    }
    
    results = {}
    
    print("开始手动映射表格和类别...")
    print("=" * 80)
    
    tables = doc.tables
    
    for table_idx, table in enumerate(tables):
        if table_idx in table_category_mapping:
            category = table_category_mapping[table_idx]
            print(f"\n表格 {table_idx + 1}: {category}")
            
            # 获取表格数据
            table_data = []
            for row in table.rows:
                row_data = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    row_data.append(cell_text)
                table_data.append(row_data)
            
            if len(table_data) < 2:
                continue
            
            # 打印表格前几行
            for j, row in enumerate(table_data[:3]):
                print(f"  行{j}: {' | '.join(row[:6])}...")
            
            # 计算统计数据
            stats = calculate_manual_statistics(table_data, category)
            if stats:
                results[category] = stats
                print(f"  -> 统计结果: 符合{stats['符合项数']}项, 部分符合{stats['部分符合项数']}项, 不符合{stats['不符合项数']}项")
            else:
                print(f"  -> 无有效统计数据")
    
    return results

def calculate_manual_statistics(table_data, category_name):
    """
    计算表格的统计数据
    """
    compliance_count = 0
    partial_compliance_count = 0
    non_compliance_count = 0
    not_applicable_count = 0
    
    # 查找包含符合情况的行
    for row in table_data:
        if len(row) > 2:
            compliance_status = row[2].strip() if len(row) > 2 else ""
            
            # 统计该行的数字
            numbers = []
            for cell in row[3:]:  # 从第4列开始统计数字
                if cell.isdigit():
                    numbers.append(int(cell))
            
            if compliance_status == "符合":
                compliance_count += sum(numbers)
            elif "部分符合" in compliance_status:
                partial_compliance_count += sum(numbers)
            elif "不符合" in compliance_status:
                non_compliance_count += sum(numbers)
            elif "不适用" in compliance_status:
                not_applicable_count += sum(numbers)
    
    total_items = compliance_count + partial_compliance_count + non_compliance_count
    
    if total_items > 0:
        return {
            '符合项数': compliance_count,
            '部分符合项数': partial_compliance_count,
            '不符合项数': non_compliance_count,
            '不适用项数': not_applicable_count,
            '总测评项数': total_items,
            '符合率': (compliance_count / total_items * 100) if total_items > 0 else 0
        }
    
    return None

def generate_manual_mapping_report(results):
    """
    生成手动映射的报告
    """
    print("\n" + "=" * 100)
    print("基于手动映射的测评对象符合数量汇总报告")
    print("=" * 100)
    
    # 按类别分组
    categories = {
        "技术要求": [
            "安全物理环境",
            "安全通信网络", 
            "安全区域边界",
            "安全计算环境-网络设备",
            "安全计算环境-安全设备", 
            "安全计算环境-服务器和终端",
            "安全计算环境-系统管理软件/平台",
            "安全计算环境-业务应用系统/平台",
            "数据资源"
        ],
        "管理要求": [
            "安全管理中心",
            "安全管理制度",
            "安全管理机构", 
            "安全管理人员",
            "安全建设管理",
            "安全运维管理"
        ]
    }
    
    grand_total = {
        '符合项数': 0,
        '部分符合项数': 0,
        '不符合项数': 0,
        '不适用项数': 0,
        '总测评项数': 0
    }
    
    # 安全计算环境汇总
    computing_env_total = {
        '符合项数': 0,
        '部分符合项数': 0,
        '不符合项数': 0,
        '不适用项数': 0,
        '总测评项数': 0
    }
    
    for group_name, category_list in categories.items():
        print(f"\n【{group_name}】")
        print("=" * 60)
        
        group_total = {
            '符合项数': 0,
            '部分符合项数': 0,
            '不符合项数': 0,
            '不适用项数': 0,
            '总测评项数': 0
        }
        
        for category in category_list:
            if category in results:
                data = results[category]
                print(f"\n  {category}:")
                print(f"    符合项数: {data['符合项数']}")
                print(f"    部分符合项数: {data['部分符合项数']}")
                print(f"    不符合项数: {data['不符合项数']}")
                print(f"    不适用项数: {data['不适用项数']}")
                print(f"    总测评项数: {data['总测评项数']}")
                print(f"    符合率: {data['符合率']:.1f}%")
                
                # 累加统计
                for key in group_total:
                    group_total[key] += data[key]
                    grand_total[key] += data[key]
                
                # 如果是安全计算环境的子类别，也累加到安全计算环境总计
                if "安全计算环境-" in category:
                    for key in computing_env_total:
                        computing_env_total[key] += data[key]
            else:
                print(f"\n  {category}: 未找到数据")
        
        # 显示组小计
        if group_total['总测评项数'] > 0:
            print(f"\n  {group_name}小计:")
            print(f"    符合项数: {group_total['符合项数']}")
            print(f"    部分符合项数: {group_total['部分符合项数']}")
            print(f"    不符合项数: {group_total['不符合项数']}")
            print(f"    不适用项数: {group_total['不适用项数']}")
            print(f"    总测评项数: {group_total['总测评项数']}")
            print(f"    符合率: {group_total['符合项数'] / group_total['总测评项数'] * 100:.1f}%")
    
    # 显示安全计算环境总计
    if computing_env_total['总测评项数'] > 0:
        print(f"\n【安全计算环境总计】")
        print("=" * 60)
        print(f"  符合项数: {computing_env_total['符合项数']}")
        print(f"  部分符合项数: {computing_env_total['部分符合项数']}")
        print(f"  不符合项数: {computing_env_total['不符合项数']}")
        print(f"  不适用项数: {computing_env_total['不适用项数']}")
        print(f"  总测评项数: {computing_env_total['总测评项数']}")
        print(f"  符合率: {computing_env_total['符合项数'] / computing_env_total['总测评项数'] * 100:.1f}%")
    
    # 显示总计
    print(f"\n" + "=" * 100)
    print("总体统计")
    print("=" * 100)
    print(f"总符合项数: {grand_total['符合项数']}")
    print(f"总部分符合项数: {grand_total['部分符合项数']}")
    print(f"总不符合项数: {grand_total['不符合项数']}")
    print(f"总不适用项数: {grand_total['不适用项数']}")
    print(f"总测评项数: {grand_total['总测评项数']}")
    if grand_total['总测评项数'] > 0:
        print(f"总体符合率: {grand_total['符合项数'] / grand_total['总测评项数'] * 100:.1f}%")
        print(f"总体符合+部分符合率: {(grand_total['符合项数'] + grand_total['部分符合项数']) / grand_total['总测评项数'] * 100:.1f}%")
    
    return results, grand_total, computing_env_total

if __name__ == "__main__":
    try:
        # 手动映射提取数据
        results = extract_compliance_manual_mapping('测评报告.docx')
        
        # 生成报告
        results, grand_total, computing_env_total = generate_manual_mapping_report(results)
        
        # 保存到文件
        with open('manual_mapping_compliance_report.txt', 'w', encoding='utf-8') as f:
            f.write("基于手动映射的测评对象符合数量报告\n")
            f.write("=" * 80 + "\n\n")
            
            # 技术要求
            f.write("【技术要求】\n")
            f.write("=" * 60 + "\n\n")
            
            tech_categories = [
                "安全物理环境",
                "安全通信网络", 
                "安全区域边界",
                "安全计算环境-网络设备",
                "安全计算环境-安全设备", 
                "安全计算环境-服务器和终端",
                "安全计算环境-系统管理软件/平台",
                "安全计算环境-业务应用系统/平台",
                "数据资源"
            ]
            
            for category in tech_categories:
                if category in results:
                    data = results[category]
                    f.write(f"{category}:\n")
                    f.write(f"  符合项数: {data['符合项数']}\n")
                    f.write(f"  部分符合项数: {data['部分符合项数']}\n")
                    f.write(f"  不符合项数: {data['不符合项数']}\n")
                    f.write(f"  不适用项数: {data['不适用项数']}\n")
                    f.write(f"  总测评项数: {data['总测评项数']}\n")
                    f.write(f"  符合率: {data['符合率']:.1f}%\n\n")
                else:
                    f.write(f"{category}: 未找到数据\n\n")
            
            # 安全计算环境总计
            if computing_env_total['总测评项数'] > 0:
                f.write("安全计算环境总计:\n")
                f.write(f"  符合项数: {computing_env_total['符合项数']}\n")
                f.write(f"  部分符合项数: {computing_env_total['部分符合项数']}\n")
                f.write(f"  不符合项数: {computing_env_total['不符合项数']}\n")
                f.write(f"  不适用项数: {computing_env_total['不适用项数']}\n")
                f.write(f"  总测评项数: {computing_env_total['总测评项数']}\n")
                f.write(f"  符合率: {computing_env_total['符合项数'] / computing_env_total['总测评项数'] * 100:.1f}%\n\n")
            
            # 管理要求
            f.write("【管理要求】\n")
            f.write("=" * 60 + "\n\n")
            
            mgmt_categories = [
                "安全管理中心",
                "安全管理制度",
                "安全管理机构", 
                "安全管理人员",
                "安全建设管理",
                "安全运维管理"
            ]
            
            for category in mgmt_categories:
                if category in results:
                    data = results[category]
                    f.write(f"{category}:\n")
                    f.write(f"  符合项数: {data['符合项数']}\n")
                    f.write(f"  部分符合项数: {data['部分符合项数']}\n")
                    f.write(f"  不符合项数: {data['不符合项数']}\n")
                    f.write(f"  不适用项数: {data['不适用项数']}\n")
                    f.write(f"  总测评项数: {data['总测评项数']}\n")
                    f.write(f"  符合率: {data['符合率']:.1f}%\n\n")
                else:
                    f.write(f"{category}: 未找到数据\n\n")
            
            # 总体统计
            f.write("总体统计:\n")
            f.write(f"总符合项数: {grand_total['符合项数']}\n")
            f.write(f"总部分符合项数: {grand_total['部分符合项数']}\n")
            f.write(f"总不符合项数: {grand_total['不符合项数']}\n")
            f.write(f"总不适用项数: {grand_total['不适用项数']}\n")
            f.write(f"总测评项数: {grand_total['总测评项数']}\n")
            if grand_total['总测评项数'] > 0:
                f.write(f"总体符合率: {grand_total['符合项数'] / grand_total['总测评项数'] * 100:.1f}%\n")
                f.write(f"总体符合+部分符合率: {(grand_total['符合项数'] + grand_total['部分符合项数']) / grand_total['总测评项数'] * 100:.1f}%\n")
        
        print(f"\n基于手动映射的报告已保存到 manual_mapping_compliance_report.txt")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
